# Google 认证问题解决方案

## 问题分析

根据你的错误日志，主要问题是：

1. **`unregistered_origin` 错误** - 当前域名 `http://127.0.0.1:5174` 未在 Google Console 中注册
2. **网络连接失败** - `Failed to fetch` 错误
3. **使用 HTTP 协议** - 某些 Google 功能在 HTTPS 下工作更好

## 立即解决方案

### 1. 更新 Google Console 配置

访问 [Google Cloud Console](https://console.cloud.google.com/)：

1. 进入你的项目
2. 导航到 "APIs & Services" > "Credentials"  
3. 找到你的 OAuth 2.0 客户端 ID
4. 在 "Authorized JavaScript origins" 中添加以下域名：
   ```
   http://127.0.0.1:5174
   http://localhost:5174
   http://127.0.0.1:5173
   http://localhost:5173
   https://127.0.0.1:5174
   https://localhost:5174
   ```

### 2. 清除浏览器数据

Google One Tap 会记住用户的选择，如果之前选择了"不显示"，需要：

- **推荐方法**：使用无痕模式测试
- **或者**：清除浏览器所有数据（设置 > 隐私和安全 > 清除浏览数据）
- **或者**：清除特定域名的数据

### 3. 检查网络连接

确保可以访问 Google 服务：
- 在浏览器中访问 `https://accounts.google.com/gsi/client`
- 检查是否有代理或防火墙阻止
- 尝试不同的网络环境

## HTTPS 开发环境（推荐）

为了获得最佳的 Google 认证体验，建议使用 HTTPS：

### 快速启动 HTTPS

```bash
# 启动 HTTPS 开发服务器（Vite 会自动生成证书）
pnpm run dev:https
```

首次访问时浏览器会显示安全警告，点击"高级" > "继续访问 localhost (不安全)"。

### 使用自定义证书（可选）

如果需要更好的证书支持：

```bash
# 运行 HTTPS 设置脚本
pnpm run setup:https
```

这会安装 `mkcert` 并生成本地信任的证书。

## 测试步骤

1. **启动开发服务器**
   ```bash
   pnpm run dev:https  # HTTPS 模式
   # 或
   pnpm run dev        # HTTP 模式
   ```

2. **访问测试页面**
   - HTTPS: `https://localhost:5174/test/google-auth`
   - HTTP: `http://127.0.0.1:5174/test/google-auth`

3. **检查配置**
   - 确认 Google Client ID 显示正确
   - 点击"测试域名和连接"按钮
   - 查看日志输出

4. **测试认证**
   - 点击"手动触发 Google One Tap"
   - 或者点击"尝试传统 OAuth 登录"

## 常见问题解决

### "One Tap not displayed: opt_out_or_no_session"
- 用户之前选择了不显示
- **解决**：使用无痕模式或清除浏览器数据

### "One Tap not displayed: invalid_client"  
- Google Client ID 配置错误
- **解决**：检查 `.dev.vars` 中的 `GOOGLE_CLIENT_ID`

### "One Tap not displayed: unregistered_origin"
- 当前域名未授权
- **解决**：在 Google Console 中添加当前域名

### "Failed to fetch"
- 网络连接问题
- **解决**：检查网络、代理、防火墙设置

## 配置检查清单

- [ ] Google Client ID 已配置在 `.dev.vars`
- [ ] Google Client Secret 已配置在 `.dev.vars`  
- [ ] `ONE_TAP_ENABLED=true` 已设置
- [ ] 当前域名已添加到 Google Console 授权列表
- [ ] 浏览器可以访问 `accounts.google.com`
- [ ] 使用无痕模式测试（排除缓存问题）

## 下一步

1. 按照上述步骤更新 Google Console 配置
2. 使用无痕模式测试
3. 如果仍有问题，尝试 HTTPS 模式
4. 查看测试页面的详细日志输出

如果问题持续存在，请提供：
- Google Console 的截图（显示授权域名配置）
- 测试页面的完整日志输出
- 当前使用的确切 URL
