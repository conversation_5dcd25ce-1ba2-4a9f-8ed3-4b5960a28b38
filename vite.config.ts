/// <reference types="vitest" />

import { cloudflareDevProxyVitePlugin, vitePlugin as remix } from "@remix-run/dev";
import path from "path";
import { defineConfig } from "vite";
import { analyzer } from "vite-bundle-analyzer";
import tsconfigPaths from "vite-tsconfig-paths";
import { getLoadContext } from "./load-context";

declare module "@remix-run/cloudflare" {
  interface Future {
    v3_singleFetch: false;
  }
}

interface GetLoadContextArgs {
  request: Request;
  context: {
    cloudflare: any; // Simplify for now
  };
}

export default defineConfig({
  // Development server configuration
  server: {
    port: 5174,
    strictPort: true, // 如果端口被占用，不要自动尝试其他端口
    host: true, // 允许外部访问
    // HTTPS 配置将通过环境变量和外部脚本处理
    // 使用 `pnpm run dev:https` 启动 HTTPS 模式
  },
  // Exclude .000 directory from all processing
  resolve: {
    alias: {
      "~": path.resolve(__dirname, "./app"),
    },
    mainFields: ["browser", "module", "main"],
  },
  plugins: [
    cloudflareDevProxyVitePlugin({
      getLoadContext: getLoadContext as any, // Type complexity with Cloudflare/Remix interfaces
    }),
    remix({
      future: {
        v3_fetcherPersist: true,
        v3_relativeSplatPath: true,
        v3_throwAbortReason: true,
        v3_singleFetch: false,
        v3_lazyRouteDiscovery: true,
      },
      // Exclude .000 directory from Remix processing
      ignoredRouteFiles: [
        "**/.*",
        "**/*.css",
        "**/*.test.{js,jsx,ts,tsx}",
        ".000/**/*",
        "**/*.server.{ts,tsx}",
      ],
    }),
    tsconfigPaths({
      // Exclude .000 directory from TypeScript path resolution
      projects: ["./tsconfig.json"],
      ignoreConfigErrors: true,
    }),
    // Bundle analyzer - only when ANALYZE=true
    ...(process.env.ANALYZE === "true"
      ? [analyzer({ analyzerMode: "static", openAnalyzer: true })]
      : []),
  ],
  ssr: {
    resolve: {
      conditions: ["workerd", "worker", "browser"],
    },
    // noExternal: ["@stackframe/react"], // Temporarily disabled to avoid global scope issues
  },
  optimizeDeps: {
    // include: ["@stackframe/react"], // Temporarily disabled to avoid global scope issues
    exclude: [],
  },
  build: {
    minify: true,
    // Enhanced build configuration for performance and smaller bundles
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching and smaller bundles
        manualChunks: (id) => {
          // Only apply manual chunks for client build
          if (id.includes("node_modules")) {
            // Core React libraries
            if (id.includes("react") || id.includes("react-dom")) {
              return "vendor";
            }
            // UI components
            if (id.includes("@radix-ui")) {
              return "ui";
            }
            // Utility libraries
            if (
              id.includes("clsx") ||
              id.includes("tailwind-merge") ||
              id.includes("class-variance-authority")
            ) {
              return "utils";
            }
            // AI SDKs - separate chunk for better caching
            if (id.includes("@ai-sdk") || id.includes("openai") || id.includes("ai/")) {
              return "ai";
            }
            // Keystatic CMS - separate chunk
            if (id.includes("@keystatic")) {
              return "keystatic";
            }
            // Icons
            if (id.includes("lucide-react")) {
              return "icons";
            }
            // Payment libraries
            if (id.includes("stripe")) {
              return "payments";
            }
            // Other large libraries
            if (id.includes("zod") || id.includes("drizzle-orm")) {
              return "validation";
            }
          }
        },
      },
      // External dependencies for Cloudflare Workers
      external: (id) => {
        // Don't bundle Node.js built-ins
        return id.startsWith("node:");
      },
    },
    // Disable source maps in production for smaller bundles
    sourcemap: false,
    // Chunk size warning limit
    chunkSizeWarningLimit: 500,
  },
});
