#!/usr/bin/env node

/**
 * 设置 HTTPS 开发环境脚本
 * 用于解决 Google 认证等需要安全上下文的功能
 */

import { execSync } from 'child_process';
import { existsSync, writeFileSync, readFileSync } from 'fs';
import { join } from 'path';

const CERT_DIR = join(process.cwd(), '.certs');
const CERT_FILE = join(CERT_DIR, 'localhost.pem');
const KEY_FILE = join(CERT_DIR, 'localhost-key.pem');

console.log('🔧 设置 HTTPS 开发环境...\n');

// 检查是否已安装 mkcert
function checkMkcert() {
  try {
    execSync('mkcert -version', { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

// 安装 mkcert
function installMkcert() {
  console.log('📦 安装 mkcert...');
  
  const platform = process.platform;
  
  try {
    if (platform === 'darwin') {
      // macOS
      execSync('brew install mkcert', { stdio: 'inherit' });
    } else if (platform === 'linux') {
      // Linux - 尝试不同的包管理器
      try {
        execSync('sudo apt-get update && sudo apt-get install -y mkcert', { stdio: 'inherit' });
      } catch {
        try {
          execSync('sudo yum install -y mkcert', { stdio: 'inherit' });
        } catch {
          console.log('❌ 无法自动安装 mkcert，请手动安装：');
          console.log('   Ubuntu/Debian: sudo apt-get install mkcert');
          console.log('   CentOS/RHEL: sudo yum install mkcert');
          console.log('   或访问: https://github.com/FiloSottile/mkcert#installation');
          process.exit(1);
        }
      }
    } else if (platform === 'win32') {
      // Windows
      try {
        execSync('choco install mkcert', { stdio: 'inherit' });
      } catch {
        try {
          execSync('scoop install mkcert', { stdio: 'inherit' });
        } catch {
          console.log('❌ 无法自动安装 mkcert，请手动安装：');
          console.log('   使用 Chocolatey: choco install mkcert');
          console.log('   使用 Scoop: scoop install mkcert');
          console.log('   或访问: https://github.com/FiloSottile/mkcert#installation');
          process.exit(1);
        }
      }
    } else {
      console.log('❌ 不支持的操作系统，请手动安装 mkcert');
      console.log('   访问: https://github.com/FiloSottile/mkcert#installation');
      process.exit(1);
    }
    
    console.log('✅ mkcert 安装成功');
  } catch (error) {
    console.error('❌ 安装 mkcert 失败:', error.message);
    process.exit(1);
  }
}

// 创建证书目录
function createCertDir() {
  if (!existsSync(CERT_DIR)) {
    execSync(`mkdir -p ${CERT_DIR}`);
    console.log('📁 创建证书目录:', CERT_DIR);
  }
}

// 安装 CA 根证书
function installCA() {
  console.log('🔐 安装 CA 根证书...');
  try {
    execSync('mkcert -install', { stdio: 'inherit' });
    console.log('✅ CA 根证书安装成功');
  } catch (error) {
    console.error('❌ 安装 CA 根证书失败:', error.message);
    process.exit(1);
  }
}

// 生成本地证书
function generateCerts() {
  console.log('📜 生成本地 HTTPS 证书...');
  
  const domains = [
    'localhost',
    '127.0.0.1',
    '::1',
    '0.0.0.0'
  ];
  
  try {
    const cmd = `mkcert -key-file ${KEY_FILE} -cert-file ${CERT_FILE} ${domains.join(' ')}`;
    execSync(cmd, { stdio: 'inherit', cwd: CERT_DIR });
    console.log('✅ 证书生成成功');
    console.log(`   证书文件: ${CERT_FILE}`);
    console.log(`   私钥文件: ${KEY_FILE}`);
  } catch (error) {
    console.error('❌ 生成证书失败:', error.message);
    process.exit(1);
  }
}

// 更新 Vite 配置
function updateViteConfig() {
  console.log('⚙️  更新 Vite 配置...');
  
  const viteConfigPath = join(process.cwd(), 'vite.config.ts');
  
  if (!existsSync(viteConfigPath)) {
    console.log('❌ 找不到 vite.config.ts 文件');
    return;
  }
  
  let config = readFileSync(viteConfigPath, 'utf-8');
  
  // 检查是否已经配置了 HTTPS
  if (config.includes('https:') && config.includes('cert:') && config.includes('key:')) {
    console.log('✅ Vite 配置已包含 HTTPS 设置');
    return;
  }
  
  // 添加 fs 导入
  if (!config.includes('import { readFileSync }')) {
    config = config.replace(
      'import path from "path";',
      'import path from "path";\nimport { readFileSync } from "fs";'
    );
  }
  
  // 更新 HTTPS 配置
  const httpsConfig = `https: process.env.HTTPS === 'true' ? {
      key: readFileSync(path.join(__dirname, '.certs/localhost-key.pem')),
      cert: readFileSync(path.join(__dirname, '.certs/localhost.pem')),
    } : false,`;
  
  config = config.replace(
    /https: process\.env\.HTTPS === 'true' \? \{[^}]*\} : false,/,
    httpsConfig
  );
  
  writeFileSync(viteConfigPath, config);
  console.log('✅ Vite 配置已更新');
}

// 创建启动脚本
function createStartScripts() {
  console.log('📝 创建启动脚本...');
  
  const packageJsonPath = join(process.cwd(), 'package.json');
  const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
  
  // 添加 HTTPS 开发脚本
  packageJson.scripts = {
    ...packageJson.scripts,
    'dev:https': 'HTTPS=true remix vite:dev',
    'dev:http': 'remix vite:dev'
  };
  
  writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log('✅ 启动脚本已添加到 package.json');
}

// 创建环境变量说明
function createEnvDocs() {
  const docsPath = join(process.cwd(), 'HTTPS_SETUP.md');
  
  const docs = `# HTTPS 开发环境设置

## 概述
此项目已配置支持 HTTPS 开发环境，主要用于解决 Google 认证等需要安全上下文的功能。

## 使用方法

### 启动 HTTPS 开发服务器
\`\`\`bash
pnpm run dev:https
\`\`\`

### 启动普通 HTTP 开发服务器
\`\`\`bash
pnpm run dev:http
# 或
pnpm run dev
\`\`\`

## Google 认证配置

### 1. 更新 Google Console 设置
在 [Google Cloud Console](https://console.cloud.google.com/) 中：

1. 进入你的项目
2. 导航到 "APIs & Services" > "Credentials"
3. 找到你的 OAuth 2.0 客户端 ID
4. 在 "Authorized JavaScript origins" 中添加：
   - \`https://localhost:5174\`
   - \`https://127.0.0.1:5174\`
   - \`http://localhost:5174\` (备用)
   - \`http://127.0.0.1:5174\` (备用)

### 2. 浏览器证书警告
首次访问 \`https://localhost:5174\` 时，浏览器会显示安全警告。
点击 "高级" -> "继续访问 localhost (不安全)" 来接受自签名证书。

### 3. 测试 Google 认证
访问 \`https://localhost:5174/test/google-auth\` 来测试 Google One Tap 功能。

## 故障排除

### 证书问题
如果遇到证书问题，重新运行设置脚本：
\`\`\`bash
node scripts/setup-https-dev.js
\`\`\`

### 端口冲突
如果 5174 端口被占用，修改 \`vite.config.ts\` 中的端口号，并更新 Google Console 中的授权域名。

### 清除浏览器数据
如果 Google One Tap 仍然不工作，尝试：
1. 使用无痕模式
2. 清除浏览器所有数据
3. 重启浏览器

## 文件说明
- \`.certs/\` - 本地 HTTPS 证书目录
- \`scripts/setup-https-dev.js\` - HTTPS 设置脚本
- \`vite.config.ts\` - 包含 HTTPS 配置的 Vite 配置文件
`;

  writeFileSync(docsPath, docs);
  console.log('✅ 创建了 HTTPS_SETUP.md 文档');
}

// 主函数
function main() {
  try {
    // 检查并安装 mkcert
    if (!checkMkcert()) {
      installMkcert();
    } else {
      console.log('✅ mkcert 已安装');
    }
    
    // 创建证书目录
    createCertDir();
    
    // 安装 CA 根证书
    installCA();
    
    // 生成本地证书
    generateCerts();
    
    // 更新 Vite 配置
    updateViteConfig();
    
    // 创建启动脚本
    createStartScripts();
    
    // 创建文档
    createEnvDocs();
    
    console.log('\n🎉 HTTPS 开发环境设置完成！');
    console.log('\n📋 下一步：');
    console.log('1. 运行 `pnpm run dev:https` 启动 HTTPS 开发服务器');
    console.log('2. 访问 https://localhost:5174 (接受证书警告)');
    console.log('3. 在 Google Console 中添加 https://localhost:5174 到授权域名');
    console.log('4. 测试 Google 认证：https://localhost:5174/test/google-auth');
    console.log('\n📖 详细说明请查看 HTTPS_SETUP.md 文件');
    
  } catch (error) {
    console.error('❌ 设置失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main();
