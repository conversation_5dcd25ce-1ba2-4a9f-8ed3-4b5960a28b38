/**
 * Google 认证路由 - 处理 Google One Tap 回调
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { redirect } from "@remix-run/cloudflare";
import { handleGoogleAuth } from "~/lib/auth/google.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const cookies = request.headers.get("Cookie") || "";

  // 从 cookie 中获取 Google credential
  const credentialMatch = cookies.match(/g_credential=([^;]+)/);
  const credential = credentialMatch ? credentialMatch[1] : null;

  if (!credential) {
    console.log("No Google credential found in cookies");
    return redirect("/auth/login?error=no_credential");
  }

  try {
    // 处理 Google 认证
    const authResult = await handleGoogleAuth(
      credential,
      request,
      context.cloudflare?.env
    );

    if (!authResult.success) {
      console.error("Google auth failed:", authResult.error);
      return redirect(`/auth/login?error=${encodeURIComponent(authResult.error || 'auth_failed')}`);
    }

    // 直接使用 Google 认证返回的 token
    const isSecure = new URL(request.url).protocol === 'https:';
    const authCookie = `auth-token=${authResult.accessToken}; Path=/; Max-Age=${7 * 24 * 60 * 60}; HttpOnly; SameSite=Lax${isSecure ? '; Secure' : ''}`;

    // 清除 Google credential cookie 并设置认证 cookie
    const headers = new Headers();
    headers.append("Set-Cookie", "g_credential=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT");
    headers.append("Set-Cookie", authCookie);

    return redirect("/console", { headers });
  } catch (error) {
    console.error("Google auth error:", error);
    return redirect("/auth/login?error=auth_error");
  }
}

// 如果有 POST 请求，也处理认证
export async function action({ request, context }: LoaderFunctionArgs) {
  return loader({ request, context });
}
